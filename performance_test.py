import time
import cv2
import numpy as np
import os
import sys
from concurrent.futures import ThreadPoolExecutor
import multiprocessing as mp

def original_method(img):
    """原始方法"""
    lower_rgb = np.array([4, 2, 4])
    upper_rgb = np.array([24, 22, 24])
    mask = cv2.inRange(img, lower_rgb, upper_rgb)

    kernel = np.ones((5, 5), np.uint8)
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    mask = cv2.medianBlur(mask, 5)

    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    h, w = img.shape[:2]
    
    results = []
    for i, cnt in enumerate(contours):
        if cv2.contourArea(cnt) < 50:
            continue

        (x, y), radius = cv2.minEnclosingCircle(cnt)
        area = cv2.contourArea(cnt)
        circle_area = np.pi * (radius ** 2)
        if area / circle_area > 0.7:
            center = (int(x), int(y))
            radius = int(radius)
            outer_radius = radius + 600

            x0 = max(center[0] - outer_radius, 0)
            y0 = max(center[1] - outer_radius, 0)
            x1 = min(center[0] + outer_radius, w)
            y1 = min(center[1] + outer_radius, h)

            roi_width = x1 - x0
            roi_height = y1 - y0
            roi = img[y0:y1, x0:x1]

            ring_mask = np.zeros((roi_height, roi_width), dtype=np.uint8)
            local_center = (center[0] - x0, center[1] - y0)

            cv2.circle(ring_mask, local_center, outer_radius, 255, thickness=-1)
            cv2.circle(ring_mask, local_center, radius, 0, thickness=-1)

            ring_cropped = cv2.bitwise_and(roi, roi, mask=ring_mask)
            results.append((ring_cropped, i))
    
    return results

def vectorized_method(img):
    """向量化优化方法"""
    def create_ring_mask_vectorized(roi_shape, center, inner_radius, outer_radius):
        h, w = roi_shape
        y, x = np.ogrid[:h, :w]
        dist_from_center = np.sqrt((x - center[0])**2 + (y - center[1])**2)
        ring_mask = ((dist_from_center >= inner_radius) & 
                     (dist_from_center <= outer_radius)).astype(np.uint8) * 255
        return ring_mask
    
    lower_rgb = np.array([4, 2, 4])
    upper_rgb = np.array([24, 22, 24])
    mask = cv2.inRange(img, lower_rgb, upper_rgb)

    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    mask = cv2.medianBlur(mask, 5)

    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    h, w = img.shape[:2]
    
    results = []
    for i, cnt in enumerate(contours):
        if cv2.contourArea(cnt) < 50:
            continue

        (x, y), radius = cv2.minEnclosingCircle(cnt)
        area = cv2.contourArea(cnt)
        circle_area = np.pi * (radius ** 2)
        if area / circle_area > 0.7:
            center = (int(x), int(y))
            radius = int(radius)
            outer_radius = radius + 600

            x0 = max(center[0] - outer_radius, 0)
            y0 = max(center[1] - outer_radius, 0)
            x1 = min(center[0] + outer_radius, w)
            y1 = min(center[1] + outer_radius, h)

            roi_width = x1 - x0
            roi_height = y1 - y0
            roi = img[y0:y1, x0:x1]
            local_center = (center[0] - x0, center[1] - y0)

            ring_mask = create_ring_mask_vectorized((roi_height, roi_width), 
                                                  local_center, radius, outer_radius)
            ring_cropped = cv2.bitwise_and(roi, roi, mask=ring_mask)
            results.append((ring_cropped, i))
    
    return results

def optimized_squared_distance_method(img):
    """使用平方距离优化的方法"""
    def create_ring_mask_optimized(roi_shape, center, inner_radius, outer_radius):
        h, w = roi_shape
        x_coords, y_coords = np.meshgrid(np.arange(w), np.arange(h))
        dx = x_coords - center[0]
        dy = y_coords - center[1]
        dist_squared = dx*dx + dy*dy
        
        inner_squared = inner_radius * inner_radius
        outer_squared = outer_radius * outer_radius
        
        ring_mask = ((dist_squared >= inner_squared) & 
                     (dist_squared <= outer_squared)).astype(np.uint8) * 255
        return ring_mask
    
    lower_rgb = np.array([4, 2, 4])
    upper_rgb = np.array([24, 22, 24])
    mask = cv2.inRange(img, lower_rgb, upper_rgb)

    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    mask = cv2.medianBlur(mask, 5)

    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    h, w = img.shape[:2]
    
    results = []
    for i, cnt in enumerate(contours):
        if cv2.contourArea(cnt) < 50:
            continue

        (x, y), radius = cv2.minEnclosingCircle(cnt)
        area = cv2.contourArea(cnt)
        circle_area = np.pi * (radius ** 2)
        if area / circle_area > 0.7:
            center = (int(x), int(y))
            radius = int(radius)
            outer_radius = radius + 600

            x0 = max(center[0] - outer_radius, 0)
            y0 = max(center[1] - outer_radius, 0)
            x1 = min(center[0] + outer_radius, w)
            y1 = min(center[1] + outer_radius, h)

            roi_width = x1 - x0
            roi_height = y1 - y0
            roi = img[y0:y1, x0:x1]
            local_center = (center[0] - x0, center[1] - y0)

            ring_mask = create_ring_mask_optimized((roi_height, roi_width), 
                                                 local_center, radius, outer_radius)
            ring_cropped = cv2.bitwise_and(roi, roi, mask=ring_mask)
            results.append((ring_cropped, i))
    
    return results

def benchmark_methods(img_path, num_runs=3):
    """基准测试不同方法"""
    if not os.path.exists(img_path):
        print(f"Error: Image file {img_path} not found")
        return
    
    img = cv2.imread(img_path)
    if img is None:
        print(f"Error: Could not load image {img_path}")
        return
    
    print(f"Testing with image: {img_path}")
    print(f"Image shape: {img.shape}")
    print(f"Number of runs: {num_runs}")
    print("=" * 60)
    
    methods = [
        ("原始方法 (cv2.circle)", original_method),
        ("向量化方法 (numpy)", vectorized_method),
        ("平方距离优化", optimized_squared_distance_method),
    ]
    
    results = {}
    
    for method_name, method_func in methods:
        print(f"\n测试 {method_name}:")
        times = []
        
        for run in range(num_runs):
            start_time = time.time()
            result = method_func(img)
            end_time = time.time()
            
            elapsed = end_time - start_time
            times.append(elapsed)
            print(f"  运行 {run+1}: {elapsed:.3f}s, 处理了 {len(result)} 个环形区域")
        
        avg_time = np.mean(times)
        std_time = np.std(times)
        results[method_name] = {
            'times': times,
            'avg': avg_time,
            'std': std_time,
            'count': len(result) if 'result' in locals() else 0
        }
        
        print(f"  平均时间: {avg_time:.3f}s ± {std_time:.3f}s")
    
    # 显示性能比较
    print("\n" + "=" * 60)
    print("性能比较总结:")
    print("=" * 60)
    
    baseline = results["原始方法 (cv2.circle)"]['avg']
    
    for method_name, data in results.items():
        speedup = baseline / data['avg']
        print(f"{method_name:20s}: {data['avg']:.3f}s (加速 {speedup:.2f}x)")
    
    return results

if __name__ == "__main__":
    # 检查是否有测试图像
    test_image = "test.bmp"
    if len(sys.argv) > 1:
        test_image = sys.argv[1]
    
    benchmark_methods(test_image)
