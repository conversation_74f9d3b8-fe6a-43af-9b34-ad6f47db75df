import time
import cv2
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import multiprocessing as mp

class RingExtractor:
    """优化的环形区域提取器，使用多种空间换时间策略"""
    
    def __init__(self, img):
        self.img = img
        self.h, self.w = img.shape[:2]
        
        # 预计算距离查找表 (空间换时间策略1)
        self.distance_lut = self._create_distance_lut()
        
    def _create_distance_lut(self):
        """创建距离查找表，避免重复计算平方根"""
        max_dist = int(np.sqrt(self.h**2 + self.w**2)) + 1
        return np.sqrt(np.arange(max_dist**2))
    
    def create_ring_mask_optimized(self, roi_shape, center, inner_radius, outer_radius):
        """使用查找表和位运算优化的环形掩码创建"""
        h, w = roi_shape
        
        # 使用meshgrid一次性创建坐标网格
        x_coords, y_coords = np.meshgrid(np.arange(w), np.arange(h))
        
        # 计算平方距离，避免开方运算
        dx = x_coords - center[0]
        dy = y_coords - center[1]
        dist_squared = dx*dx + dy*dy
        
        # 使用平方距离比较，避免开方
        inner_squared = inner_radius * inner_radius
        outer_squared = outer_radius * outer_radius
        
        # 创建环形掩码
        ring_mask = ((dist_squared >= inner_squared) & 
                     (dist_squared <= outer_squared)).astype(np.uint8) * 255
        
        return ring_mask
    
    def extract_ring_batch(self, contour_data_list):
        """批量处理多个轮廓"""
        results = []
        
        for cnt, i in contour_data_list:
            if cv2.contourArea(cnt) < 50:
                continue

            (x, y), radius = cv2.minEnclosingCircle(cnt)
            area = cv2.contourArea(cnt)
            circle_area = np.pi * (radius ** 2)
            
            if area / circle_area <= 0.7:
                continue
                
            center = (int(x), int(y))
            radius = int(radius)
            outer_radius = radius + 600

            # 计算ROI边界
            x0 = max(center[0] - outer_radius, 0)
            y0 = max(center[1] - outer_radius, 0)
            x1 = min(center[0] + outer_radius, self.w)
            y1 = min(center[1] + outer_radius, self.h)

            roi_width = x1 - x0
            roi_height = y1 - y0

            # 提取ROI
            roi = self.img[y0:y1, x0:x1]
            local_center = (center[0] - x0, center[1] - y0)

            # 使用优化的掩码创建方法
            ring_mask = self.create_ring_mask_optimized(
                (roi_height, roi_width), local_center, radius, outer_radius)

            # 应用掩码
            ring_cropped = cv2.bitwise_and(roi, roi, mask=ring_mask)
            
            results.append((ring_cropped, i))
            
        return results

def process_contours_parallel(img, contours, num_workers=None):
    """并行处理轮廓"""
    if num_workers is None:
        num_workers = min(mp.cpu_count(), 4)
    
    extractor = RingExtractor(img)
    
    # 预过滤和分组轮廓
    valid_contours = []
    for i, cnt in enumerate(contours):
        if cv2.contourArea(cnt) >= 50:
            _, radius = cv2.minEnclosingCircle(cnt)
            area = cv2.contourArea(cnt)
            circle_area = np.pi * (radius ** 2)
            if area / circle_area > 0.7:
                valid_contours.append((cnt, i))
    
    if not valid_contours:
        return []
    
    # 将轮廓分组以便并行处理
    chunk_size = max(1, len(valid_contours) // num_workers)
    contour_chunks = [valid_contours[i:i + chunk_size] 
                     for i in range(0, len(valid_contours), chunk_size)]
    
    # 并行处理
    all_results = []
    with ThreadPoolExecutor(max_workers=num_workers) as executor:
        futures = [executor.submit(extractor.extract_ring_batch, chunk) 
                  for chunk in contour_chunks]
        
        for future in futures:
            results = future.result()
            all_results.extend(results)
    
    return all_results

def main_optimized():
    """优化版本的主函数"""
    start_time = time.time()
    
    # 使用更高效的图像读取
    img = cv2.imread("test.bmp", cv2.IMREAD_COLOR)
    if img is None:
        print("Error: Could not load image")
        return
    
    print(f"Image loaded: {img.shape}")
    
    # 颜色范围检测
    lower_rgb = np.array([4, 2, 4])
    upper_rgb = np.array([24, 22, 24])
    mask = cv2.inRange(img, lower_rgb, upper_rgb)

    # 优化的形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    mask = cv2.medianBlur(mask, 5)

    # 轮廓检测
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    print(f"Found {len(contours)} contours")
    
    # 并行处理轮廓
    results = process_contours_parallel(img, contours)
    
    print(f"Processed {len(results)} valid rings")
    
    # 保存结果
    for ring_cropped, i in results:
        cv2.imwrite(f"ring_{i}.bmp", ring_cropped)
    
    end_time = time.time()
    print(f"Optimized time taken: {end_time - start_time:.3f} seconds")

def main_comparison():
    """比较原版和优化版的性能"""
    print("=== 性能比较 ===")
    
    # 运行优化版本
    print("\n运行优化版本:")
    main_optimized()
    
    print("\n运行原版本:")
    # 这里可以调用原版本进行比较
    # main_original()

if __name__ == "__main__":
    main_comparison()
