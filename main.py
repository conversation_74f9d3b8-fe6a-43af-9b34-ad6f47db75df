import time
import cv2
import numpy as np
from concurrent.futures import ThreadPoolExecutor

def create_ring_mask_vectorized(roi_shape, center, inner_radius, outer_radius):
    """使用向量化操作创建环形掩码，比cv2.circle更快"""
    h, w = roi_shape
    y, x = np.ogrid[:h, :w]

    # 计算到中心点的距离
    dist_from_center = np.sqrt((x - center[0])**2 + (y - center[1])**2)

    # 创建环形掩码：距离在内圆和外圆之间的区域
    ring_mask = ((dist_from_center >= inner_radius) &
                 (dist_from_center <= outer_radius)).astype(np.uint8) * 255

    return ring_mask

def process_single_contour(args):
    """处理单个轮廓的函数，用于并行处理"""
    cnt, img, i, h, w = args

    if cv2.contourArea(cnt) < 50:
        return None

    (x, y), radius = cv2.minEnclosingCircle(cnt)
    area = cv2.contourArea(cnt)
    circle_area = np.pi * (radius ** 2)

    if area / circle_area <= 0.7:
        return None

    center = (int(x), int(y))
    radius = int(radius)
    outer_radius = radius + 600

    # 计算ROI边界
    x0 = max(center[0] - outer_radius, 0)
    y0 = max(center[1] - outer_radius, 0)
    x1 = min(center[0] + outer_radius, w)
    y1 = min(center[1] + outer_radius, h)

    roi_width = x1 - x0
    roi_height = y1 - y0

    # 提取ROI
    roi = img[y0:y1, x0:x1]
    local_center = (center[0] - x0, center[1] - y0)

    # 使用向量化方法创建环形掩码
    ring_mask = create_ring_mask_vectorized((roi_height, roi_width),
                                          local_center, radius, outer_radius)

    # 应用掩码
    ring_cropped = cv2.bitwise_and(roi, roi, mask=ring_mask)

    return ring_cropped, i

def main():
    start_time = time.time()
    img = cv2.imread("test.bmp")

    lower_rgb = np.array([4, 2, 4])
    upper_rgb = np.array([24, 22, 24])
    mask = cv2.inRange(img, lower_rgb, upper_rgb)

    # 优化形态学操作：使用更高效的核
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    mask = cv2.medianBlur(mask, 5)

    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    h, w = img.shape[:2]

    # 预过滤轮廓，减少后续处理量
    valid_contours = []
    for i, cnt in enumerate(contours):
        if cv2.contourArea(cnt) >= 50:
            _, radius = cv2.minEnclosingCircle(cnt)
            area = cv2.contourArea(cnt)
            circle_area = np.pi * (radius ** 2)
            if area / circle_area > 0.7:
                valid_contours.append((cnt, img, i, h, w))

    print(f"Found {len(valid_contours)} valid contours to process")

    # 并行处理轮廓
    results = []
    with ThreadPoolExecutor(max_workers=min(4, len(valid_contours))) as executor:
        futures = executor.map(process_single_contour, valid_contours)
        results = [result for result in futures if result is not None]

    # 保存结果
    for ring_cropped, i in results:
        cv2.imwrite(f"ring_{i}.bmp", ring_cropped)

    end_time = time.time()
    print(f"Time taken: {end_time - start_time} seconds")

if __name__ == "__main__":
    main()
